{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__860be4f0._.js", "server/edge/chunks/edge-wrapper_1985d09c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Bo+sQ9ioaiyiLUwLjzd1udTV8v8fmaLeb82li3vq6m4=", "__NEXT_PREVIEW_MODE_ID": "c78f24cb7ff9595b96d2a970e49fbe95", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2a3503a3f2d42f74de388807c848ab411eda738b6286c52f0e07528c57f5083f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a1a8af2152797227f2da29910cf313a8a3822aa6cfe99f242321aa50b87d6fc3"}}}, "instrumentation": null, "functions": {}}